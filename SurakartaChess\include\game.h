#ifndef GAME_H
#define GAME_H

#include <stdbool.h>
#include "config.h"

// 全局变量声明
extern int T; // 当前玩家，1表示上方，-1表示下方

// 移动结构体
typedef struct {
    int from_x, from_y;  // 起始位置 (1-based)
    int to_x, to_y;      // 目标位置 (1-based)
    int score;           // 移动评分
    bool is_capture;     // 是否为吃子移动
} Move;

// 游戏主要功能
void print(int a[BOARD_SIZE][BOARD_SIZE]);
void zouchizi(int x, int y, int c, int d, int a[BOARD_SIZE][BOARD_SIZE]);
void zong(int a[BOARD_SIZE][BOARD_SIZE]);
void zhuanhuan(int a[BOARD_SIZE][BOARD_SIZE], int b[BOARD_SIZE][BOARD_SIZE]);

// 游戏规则检查函数
bool is_valid_move(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player);
bool is_valid_move_silent(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player);
bool is_adjacent_move(int from_x, int from_y, int to_x, int to_y);
bool can_capture_along_arc(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player);
bool check_arc_path(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player, int arc_direction);
bool is_game_over(int board[BOARD_SIZE][BOARD_SIZE]);
int count_pieces(int board[BOARD_SIZE][BOARD_SIZE], int player);

// 移动生成和AI相关函数
int generate_valid_moves(int board[BOARD_SIZE][BOARD_SIZE], int player, Move moves[], int max_moves);
int evaluate_move(int board[BOARD_SIZE][BOARD_SIZE], Move* move, int player);
Move select_best_move(int board[BOARD_SIZE][BOARD_SIZE], int player);

// 原有功能保留
void initialize_game();
void start_game();
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side);
bool get_player_move(int board[BOARD_SIZE][BOARD_SIZE], int* from_x, int* from_y, int* to_x, int* to_y);
void switch_player();
void display_game_status(int board[BOARD_SIZE][BOARD_SIZE]);

// 游戏设置
int choose_side();
int choose_first_player();

#endif // GAME_H
