#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "../include/game.h"
#include "../include/config.h"

extern int T;

int main() {
    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 基于原始test.cpp的主函数逻辑
    int b[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };

    int BBQ, s;
    int x, y, c, d, i;

    print(b);
    printf("Enter 1 for upper side, 2 for lower side: ");
    scanf("%d", &BBQ);
    printf("  * Game Start *\n");
    printf("  1 Human first\n");
    printf("  2 Computer first\n");

    if (BBQ == 1) {
        // 人类选择上方(+)，AI是下方(-)
        printf("You are upper side (+), AI is lower side (-)\n");
        printf("Enter 1 or 2 to choose first player:\n");
        scanf("%d", &s);

        if (s == 1) {
            // 人类先手，人类是上方(+)
            T = 1;
            for (i = 0; i < 100; i++) {
                // 检查游戏是否结束
                if (is_game_over(b)) break;

                // 人类回合
                printf("Your turn (+ pieces):\n");
                do {
                    printf("Enter piece position (row,col):\n");
                    scanf("%d,%d", &x, &y);
                    printf("Enter target position (row,col):\n");
                    scanf("%d,%d", &c, &d);
                    if (is_valid_move(b, x, y, c, d, T)) {
                        zouchizi(x, y, c, d, b);
                        print(b);
                        break;
                    } else {
                        printf("Invalid move! Try again.\n");
                    }
                } while (1);

                // 检查游戏是否结束
                if (is_game_over(b)) break;

                // AI回合
                T = -1;  // 切换到AI(下方)
                printf("AI turn (- pieces):\n");
                zong(b);
                print(b);
                T = 1;   // 切换回人类(上方)
            }
        } else if (s == 2) {
            // AI先手，AI是下方(-)
            T = -1;
            for (i = 0; i < 100; i++) {
                // 检查游戏是否结束
                if (is_game_over(b)) break;

                // AI回合
                printf("AI turn (- pieces):\n");
                zong(b);
                print(b);

                // 检查游戏是否结束
                if (is_game_over(b)) break;

                // 人类回合
                T = 1;   // 切换到人类(上方)
                printf("Your turn (+ pieces):\n");
                do {
                    printf("Enter piece position (row,col):\n");
                    scanf("%d,%d", &x, &y);
                    printf("Enter target position (row,col):\n");
                    scanf("%d,%d", &c, &d);
                    if (is_valid_move(b, x, y, c, d, T)) {
                        zouchizi(x, y, c, d, b);
                        print(b);
                        break;
                    } else {
                        printf("Invalid move! Try again.\n");
                    }
                } while (1);
                T = -1;  // 切换回AI(下方)
            }
        }
    } else if (BBQ == 2) {
        // 人类选择下方(-)，AI是上方(+)
        printf("You are lower side (-), AI is upper side (+)\n");
        printf("Enter 1 or 2 to choose first player:\n");
        scanf("%d", &s);

        if (s == 1) {
            // 人类先手，人类是下方(-)
            T = -1;
            for (i = 0; i < 100; i++) {
                // 人类回合
                printf("Your turn (- pieces):\n");
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                if (is_valid_move(b, x, y, c, d, T)) {
                    zouchizi(x, y, c, d, b);
                    print(b);
                } else {
                    printf("Invalid move! Try again.\n");
                    continue;
                }

                // AI回合
                T = 1;   // 切换到AI(上方)
                printf("AI turn (+ pieces):\n");
                zong(b);
                print(b);
                T = -1;  // 切换回人类(下方)
            }
        } else if (s == 2) {
            // AI先手，AI是上方(+)
            T = 1;
            for (i = 0; i < 100; i++) {
                // AI回合
                printf("AI turn (+ pieces):\n");
                zong(b);
                print(b);

                // 人类回合
                T = -1;  // 切换到人类(下方)
                printf("Your turn (- pieces):\n");
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                if (is_valid_move(b, x, y, c, d, T)) {
                    zouchizi(x, y, c, d, b);
                    print(b);
                } else {
                    printf("Invalid move! Try again.\n");
                    continue;
                }
                T = 1;   // 切换回AI(上方)
            }
        }
    }

    return 0;
}
