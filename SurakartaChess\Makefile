# Makefile for Surakarta Chess

# 编译器设置
CXX = g++
CXXFLAGS = -Wall -Wextra -std=c++11 -O2

# 目录设置
SRCDIR = src
INCDIR = include
OBJDIR = obj
BINDIR = bin

# 目标文件
TARGET = $(BINDIR)/surakarta_chess

# 源文件
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)

# 头文件依赖
INCLUDES = -I$(INCDIR)

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)
	@mkdir -p $(BINDIR)

# 链接目标文件
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $@
	@echo "Build complete: $(TARGET)"

# 编译源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	rm -rf $(OBJDIR) $(BINDIR)
	@echo "Clean complete"

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 调试版本
debug: CXXFLAGS += -g -DDEBUG
debug: clean all

# 发布版本
release: CXXFLAGS += -DNDEBUG
release: clean all

# 安装（可选）
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# 卸载（可选）
uninstall:
	rm -f /usr/local/bin/surakarta_chess

# 显示帮助
help:
	@echo "Available targets:"
	@echo "  all      - Build the program (default)"
	@echo "  clean    - Remove build files"
	@echo "  run      - Build and run the program"
	@echo "  debug    - Build debug version"
	@echo "  release  - Build release version"
	@echo "  install  - Install to system"
	@echo "  uninstall- Remove from system"
	@echo "  help     - Show this help"

# 声明伪目标
.PHONY: all clean run debug release install uninstall help directories
