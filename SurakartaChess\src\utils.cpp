#include "../include/utils.h"
#include "../include/config.h"

// 找到数组中的最大值
int find_max(int arr[], int size) {
    int max_val = 0;
    for (int i = 0; i < size; i++) {
        if (arr[i] != 0) {
            max_val = arr[i];
            break;
        }
    }
    
    for (int i = 0; i < size; i++) {
        if (arr[i] >= max_val && arr[i] != 0) {
            max_val = arr[i];
        }
    }
    return max_val;
}

// 找到数组中的最小值
int find_min(int arr[], int size) {
    int min_val = 0;
    for (int i = 0; i < size; i++) {
        if (arr[i] != 0) {
            min_val = arr[i];
            break;
        }
    }
    
    for (int i = 0; i < size; i++) {
        if (arr[i] <= min_val && arr[i] != 0) {
            min_val = arr[i];
        }
    }
    return min_val;
}

// 找到最大值的索引
int find_max_index(int arr[], int size) {
    int max_val = 0;
    int max_index = 0;
    
    for (int i = 0; i < size; i++) {
        if (arr[i] != 0) {
            max_val = arr[i];
            max_index = i;
            break;
        }
    }
    
    for (int i = 0; i < size; i++) {
        if (arr[i] >= max_val && arr[i] != 0) {
            max_val = arr[i];
            max_index = i;
        }
    }
    return max_index;
}

// 处理更大数组的最大值
int find_max2(int arr[], int size) {
    int max_val = 0;
    for (int i = 0; i < size; i++) {
        if (arr[i] != 0) {
            max_val = arr[i];
            break;
        }
    }
    
    for (int i = 0; i < size; i++) {
        if (arr[i] >= max_val && arr[i] != 0) {
            max_val = arr[i];
        }
    }
    return max_val;
}

// 处理更大数组的最小值
int find_min2(int arr[], int size) {
    int min_val = 0;
    for (int i = 0; i < size; i++) {
        if (arr[i] != 0) {
            min_val = arr[i];
            break;
        }
    }
    
    for (int i = 0; i < size; i++) {
        if (arr[i] <= min_val && arr[i] != 0) {
            min_val = arr[i];
        }
    }
    return min_val;
}

// count_pieces函数已移动到game.cpp中

// 计算某个玩家的移动能力
int calculate_mobility(int board[BOARD_SIZE][BOARD_SIZE], int player) {
    int mobility = 0;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == player) {
                // 检查8个方向的移动可能性
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        int ni = i + di;
                        int nj = j + dj;
                        if (ni >= 0 && ni < BOARD_SIZE && nj >= 0 && nj < BOARD_SIZE) {
                            if (board[ni][nj] == EMPTY) {
                                mobility++;
                            }
                        }
                    }
                }
            }
        }
    }
    return mobility;
}

// is_game_over函数已移动到game.cpp中
