#include <stdio.h>
#include "../include/ai.h"
#include "../include/board.h"
#include "../include/utils.h"
#include "../include/config.h"

// 外部全局变量
extern int T;

// AI决策主函数
void make_ai_move(int board[BOARD_SIZE][BOARD_SIZE], int player) {
    int my_pieces = count_pieces(board, player);
    int opponent_pieces = count_pieces(board, -player);
    
    // 根据棋子数量选择不同的策略
    if ((my_pieces == 2) && (opponent_pieces > 2)) {
        minimax_level1(board);  // 四层搜索
    } else if ((opponent_pieces == 2) && (my_pieces > 2)) {
        minimax_level1(board);  // 对应原来的pinggu111
    } else if (my_pieces == 1 || opponent_pieces == 1) {
        handle_endgame(board);  // 残局处理
    } else {
        handle_normal_game(board);  // 正常游戏
    }
}

// 评估函数
int evaluate_position(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]) {
    int my_pieces = 0, opponent_pieces = 0;
    int my_mobility = 0, opponent_mobility = 0;
    int my_position_value = 0, opponent_position_value = 0;
    
    // 不同阶段的位置价值表
    int early_game_values[BOARD_SIZE][BOARD_SIZE] = {
        {25, 35, 35, 35, 35, 25},
        {35, 70, 50, 50, 70, 35},
        {35, 50, 60, 60, 50, 35},
        {35, 50, 60, 60, 50, 35},
        {35, 70, 50, 50, 70, 35},
        {25, 35, 35, 35, 35, 25}
    };
    
    int mid_game_values[BOARD_SIZE][BOARD_SIZE] = {
        {15, 35, 35, 35, 35, 15},
        {35, 60, 50, 50, 60, 35},
        {35, 50, 60, 60, 50, 35},
        {35, 50, 60, 60, 50, 35},
        {35, 60, 50, 50, 60, 35},
        {15, 35, 35, 35, 35, 15}
    };
    
    int end_game_values[BOARD_SIZE][BOARD_SIZE] = {
        {8, 35, 35, 35, 35, 8},
        {35, 50, 70, 70, 50, 35},
        {35, 70, 60, 60, 70, 35},
        {35, 70, 60, 60, 70, 35},
        {35, 50, 70, 70, 50, 35},
        {8, 35, 35, 35, 35, 8}
    };
    
    // 统计棋子和计算位置价值
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == T) {
                my_pieces++;
                // 计算移动能力
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        int ni = i + di, nj = j + dj;
                        if (ni >= 0 && ni < BOARD_SIZE && nj >= 0 && nj < BOARD_SIZE) {
                            if (board[ni][nj] == EMPTY) {
                                my_mobility++;
                            }
                        }
                    }
                }
                
                // 根据游戏阶段选择位置价值
                if (my_pieces > 8) {
                    my_position_value += early_game_values[i][j];
                } else if (my_pieces > 4) {
                    my_position_value += mid_game_values[i][j];
                } else {
                    my_position_value += end_game_values[i][j];
                }
            } else if (board[i][j] == -T) {
                opponent_pieces++;
                // 计算对手移动能力
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        int ni = i + di, nj = j + dj;
                        if (ni >= 0 && ni < BOARD_SIZE && nj >= 0 && nj < BOARD_SIZE) {
                            if (board[ni][nj] == EMPTY) {
                                opponent_mobility++;
                            }
                        }
                    }
                }
                
                // 对手位置价值
                if (opponent_pieces > 8) {
                    opponent_position_value += early_game_values[i][j];
                } else if (opponent_pieces > 4) {
                    opponent_position_value += mid_game_values[i][j];
                } else {
                    opponent_position_value += end_game_values[i][j];
                }
            }
        }
    }
    
    int total_pieces = my_pieces + opponent_pieces;
    int evaluation;
    
    // 根据游戏阶段调整评估权重
    if (total_pieces > 18) {
        evaluation = (my_pieces * 200 + my_mobility * 6 + my_position_value * 4) -
                    (opponent_pieces * 100 + opponent_mobility * 3 + opponent_position_value * 2) +
                    bonus[0] * 60;
    } else if (total_pieces > 8) {
        evaluation = (my_pieces * 200 + my_mobility * 6 + my_position_value * 4) -
                    (opponent_pieces * 100 + opponent_mobility * 3 + opponent_position_value * 2) +
                    bonus[0] * 150;
    } else {
        evaluation = (my_pieces * 240 + my_mobility * 6 + my_position_value * 4) -
                    (opponent_pieces * 100 + opponent_mobility * 3 + opponent_position_value * 2) +
                    bonus[0] * 150;
    }
    
    return evaluation;
}

// Minimax第一层 - 选择最佳移动
void minimax_level1(int board[BOARD_SIZE][BOARD_SIZE]) {
    int best_scores[12] = {0};
    int best_moves[12] = {0};
    int piece_positions_x[12] = {0};
    int piece_positions_y[12] = {0};
    int piece_count = 0;

    int temp_board[BOARD_SIZE][BOARD_SIZE];
    int bonus[1] = {0};

    // 遍历所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == T) {
                int move_scores[12] = {0};

                // 尝试所有可能的移动
                int move_index = 0;
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        int ni = i + di, nj = j + dj;
                        if (ni >= 0 && ni < BOARD_SIZE && nj >= 0 && nj < BOARD_SIZE) {
                            if (board[ni][nj] == EMPTY) {
                                // 复制棋盘并执行移动
                                copy_board(board, temp_board);
                                temp_board[ni][nj] = T;
                                temp_board[i][j] = EMPTY;

                                // 评估这个移动
                                move_scores[move_index] = minimax_level2(temp_board, bonus);
                                move_index++;
                            }
                        }
                    }
                }

                // 检查特殊吃子移动
                if (check_capture_direction1(board, i, j) == CAPTURE_BONUS) {
                    copy_board(board, temp_board);
                    execute_capture1(temp_board, i, j);
                    bonus[0] = 3;
                    move_scores[8] = minimax_level2(temp_board, bonus);
                    bonus[0] = 0;
                }

                // 记录最佳移动
                best_scores[piece_count] = find_max(move_scores, 12);
                best_moves[piece_count] = find_max_index(move_scores, 12);
                piece_positions_x[piece_count] = i;
                piece_positions_y[piece_count] = j;
                piece_count++;
            }
        }
    }

    // 选择全局最佳移动
    int best_piece_index = find_max_index(best_scores, piece_count);
    int best_x = piece_positions_x[best_piece_index];
    int best_y = piece_positions_y[best_piece_index];
    int best_move = best_moves[best_piece_index];

    // 执行最佳移动
    execute_best_move(board, best_x, best_y, best_move);
}

// 执行最佳移动的辅助函数
void execute_best_move(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int move_type) {
    int directions[8][2] = {{-1,-1}, {-1,0}, {-1,1}, {0,-1}, {0,1}, {1,-1}, {1,0}, {1,1}};

    if (move_type < 8) {
        // 普通移动
        int dx = directions[move_type][0];
        int dy = directions[move_type][1];
        int new_x = x + dx;
        int new_y = y + dy;

        if (new_x >= 0 && new_x < BOARD_SIZE && new_y >= 0 && new_y < BOARD_SIZE) {
            board[new_x][new_y] = board[x][y];
            board[x][y] = EMPTY;
        }
    } else {
        // 特殊吃子移动
        switch (move_type) {
            case 8: execute_capture1(board, x, y); break;
            case 9: execute_capture2(board, x, y); break;
            case 10: execute_capture3(board, x, y); break;
            case 11: execute_capture4(board, x, y); break;
        }
    }
}

// Minimax第二层
int minimax_level2(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]) {
    int move_scores[12] = {0};
    int move_count = 0;
    int temp_board[BOARD_SIZE][BOARD_SIZE];

    // 遍历对手的所有可能移动
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == -T) {
                // 尝试普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        int ni = i + di, nj = j + dj;
                        if (ni >= 0 && ni < BOARD_SIZE && nj >= 0 && nj < BOARD_SIZE) {
                            if (board[ni][nj] == EMPTY) {
                                copy_board(board, temp_board);
                                temp_board[ni][nj] = -T;
                                temp_board[i][j] = EMPTY;

                                move_scores[move_count] = minimax_level3(temp_board, bonus);
                                move_count++;
                                if (move_count >= 12) break;
                            }
                        }
                    }
                    if (move_count >= 12) break;
                }
                if (move_count >= 12) break;
            }
        }
        if (move_count >= 12) break;
    }

    return find_min(move_scores, move_count);
}

// 简化的第三层实现
int minimax_level3(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]) {
    return evaluate_position(board, bonus);
}

// 其他层级的简化实现
int minimax_level4(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]) {
    return evaluate_position(board, bonus);
}

int minimax_level5(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]) {
    return evaluate_position(board, bonus);
}

// 特殊情况处理函数
void handle_endgame(int board[BOARD_SIZE][BOARD_SIZE]) {
    // 残局时优先考虑吃子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == T) {
                // 检查是否可以吃子
                if (check_capture_direction1(board, i, j) == CAPTURE_BONUS) {
                    execute_capture1(board, i, j);
                    return;
                }
                if (check_capture_direction2(board, i, j) == CAPTURE_BONUS) {
                    execute_capture2(board, i, j);
                    return;
                }
                if (check_capture_direction3(board, i, j) == CAPTURE_BONUS) {
                    execute_capture3(board, i, j);
                    return;
                }
                if (check_capture_direction4(board, i, j) == CAPTURE_BONUS) {
                    execute_capture4(board, i, j);
                    return;
                }
            }
        }
    }

    // 如果不能吃子，使用普通策略
    minimax_level1(board);
}

void handle_few_pieces(int board[BOARD_SIZE][BOARD_SIZE]) {
    minimax_level1(board);
}

void handle_normal_game(int board[BOARD_SIZE][BOARD_SIZE]) {
    minimax_level1(board);
}

// Alpha-Beta剪枝版本的简化实现
int minimax_ab_level3(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]) {
    (void)cutoff; // 标记未使用参数，避免警告
    return evaluate_position(board, bonus);
}

int minimax_ab_level4(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]) {
    (void)cutoff; // 标记未使用参数，避免警告
    return evaluate_position(board, bonus);
}

int minimax_ab_level5(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]) {
    (void)cutoff; // 标记未使用参数，避免警告
    return evaluate_position(board, bonus);
}
