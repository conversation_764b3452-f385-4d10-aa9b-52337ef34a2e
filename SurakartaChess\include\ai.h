#ifndef AI_H
#define AI_H

#include "config.h"

// AI决策相关函数
void make_ai_move(int board[BOARD_SIZE][BOARD_SIZE], int player);
void execute_best_move(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int move_type);

// 评估函数
int evaluate_position(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]);

// Minimax算法的不同层级
void minimax_level1(int board[BOARD_SIZE][BOARD_SIZE]);
int minimax_level2(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]);
int minimax_level3(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]);
int minimax_level4(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]);
int minimax_level5(int board[BOARD_SIZE][BOARD_SIZE], int bonus[]);

// Alpha-Beta剪枝版本
int minimax_ab_level3(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]);
int minimax_ab_level4(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]);
int minimax_ab_level5(int board[BOARD_SIZE][BOARD_SIZE], int bonus[], int cutoff[]);

// 特殊情况处理
void handle_endgame(int board[BOARD_SIZE][BOARD_SIZE]);
void handle_few_pieces(int board[BOARD_SIZE][BOARD_SIZE]);
void handle_normal_game(int board[BOARD_SIZE][BOARD_SIZE]);

#endif // AI_H
