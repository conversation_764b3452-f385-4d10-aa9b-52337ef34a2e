# 苏拉卡尔塔棋 (Surakarta Chess)

一个基于C++实现的苏拉卡尔塔棋AI博弈程序，具有完整的工程化结构和智能AI对手。

## 游戏简介

苏拉卡尔塔棋是一种策略棋类游戏，具有以下特点：
- 6×6的棋盘
- 每方12个棋子，分别位于棋盘的上下两端
- 棋子可以直线移动到相邻空位
- 特殊的"环形吃子"规则：棋子可以沿着棋盘边缘的环形路径吃掉对方棋子
- 目标：将对方棋子减少到1个或0个

## 项目特性

### 🏗️ 工程化设计
- **模块化架构**：代码按功能分离为不同模块
- **头文件分离**：清晰的接口定义
- **可移植性**：标准C++实现，跨平台兼容
- **易于维护**：良好的代码组织和注释

### 🤖 智能AI系统
- **Minimax算法**：多层搜索决策
- **Alpha-Beta剪枝**：优化搜索效率
- **动态策略**：根据棋局阶段调整策略
- **位置评估**：考虑棋子位置价值和移动能力

### 🎮 用户友好
- **直观界面**：清晰的棋盘显示
- **灵活设置**：可选择阵营和先手
- **实时反馈**：显示游戏状态和提示

## 项目结构

```
SurakartaChess/
├── include/           # 头文件目录
│   ├── config.h      # 配置常量
│   ├── game.h        # 游戏逻辑接口
│   ├── ai.h          # AI算法接口
│   ├── board.h       # 棋盘操作接口
│   └── utils.h       # 工具函数接口
├── src/              # 源文件目录
│   ├── main.cpp      # 主程序入口
│   ├── game.cpp      # 游戏逻辑实现
│   ├── ai.cpp        # AI算法实现
│   ├── board.cpp     # 棋盘操作实现
│   └── utils.cpp     # 工具函数实现
├── Makefile          # 构建配置
└── README.md         # 项目文档
```

## 编译和运行

### 系统要求
- C++11或更高版本的编译器
- Make工具（可选，用于使用Makefile）

### 编译方法

#### 使用Makefile（推荐）
```bash
# 编译程序
make

# 编译并运行
make run

# 编译调试版本
make debug

# 清理编译文件
make clean
```

#### 手动编译
```bash
# 创建目录
mkdir -p obj bin

# 编译所有源文件
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/*.cpp
mv *.o obj/

# 链接生成可执行文件
g++ obj/*.o -o bin/surakarta_chess
```

### 运行程序
```bash
# 如果使用Makefile编译
./bin/surakarta_chess

# 或者使用make运行
make run
```

## 使用说明

1. **启动游戏**：运行程序后按提示操作
2. **选择阵营**：选择上方(●)或下方(○)
3. **选择先手**：选择人类先手或电脑先手
4. **移动棋子**：输入格式为"行,列"，例如"2,3"
5. **游戏结束**：当一方棋子减少到1个或0个时游戏结束

## 技术实现

### AI算法
- **搜索深度**：根据棋局复杂度动态调整
- **评估函数**：综合考虑棋子数量、位置价值、移动能力
- **特殊处理**：残局、少子局面的专门策略

### 代码特点
- **内存安全**：避免缓冲区溢出和内存泄漏
- **异常处理**：输入验证和错误处理
- **性能优化**：高效的算法实现

## 扩展和定制

### 配置修改
编辑 `include/config.h` 可以调整：
- 搜索深度
- 评估权重
- 游戏参数

### 功能扩展
- 添加更多AI难度级别
- 实现网络对战功能
- 添加游戏记录和回放
- 图形界面支持

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目！

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
